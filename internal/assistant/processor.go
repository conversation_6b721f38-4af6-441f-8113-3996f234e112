package assistant

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"github.com/koopa0/assistant-go/internal/config"
	"github.com/koopa0/assistant-go/internal/storage/postgres"
	"github.com/koopa0/assistant-go/internal/tools"
)

// Processor handles request processing pipeline
type Processor struct {
	config   *config.Config
	db       *postgres.Client
	registry *tools.Registry
	logger   *slog.Logger
	context  *ContextManager
}

// NewProcessor creates a new processor
func NewProcessor(cfg *config.Config, db *postgres.Client, registry *tools.Registry, logger *slog.Logger) (*Processor, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config is required")
	}
	if db == nil {
		return nil, fmt.Errorf("database client is required")
	}
	if registry == nil {
		return nil, fmt.Errorf("tool registry is required")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger is required")
	}

	contextManager, err := NewContextManager(db, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create context manager: %w", err)
	}

	return &Processor{
		config:   cfg,
		db:       db,
		registry: registry,
		logger:   logger,
		context:  contextManager,
	}, nil
}

// Process processes a query request through the pipeline
func (p *Processor) Process(ctx context.Context, request *QueryRequest) (*QueryResponse, error) {
	if request == nil {
		return nil, NewInvalidInputError("request is required", nil)
	}

	startTime := time.Now()

	p.logger.Debug("Starting request processing",
		slog.String("query", request.Query),
		slog.Any("conversation_id", request.ConversationID))

	// Step 1: Validate and prepare request
	if err := p.validateRequest(request); err != nil {
		return nil, fmt.Errorf("request validation failed: %w", err)
	}

	// Step 2: Get or create conversation context
	conversation, err := p.getOrCreateConversation(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("failed to get conversation context: %w", err)
	}

	// Step 3: Add user message to conversation
	_, err = p.context.AddMessage(ctx, conversation.ID, "user", request.Query, request.Context)
	if err != nil {
		return nil, fmt.Errorf("failed to add user message: %w", err)
	}

	// Step 4: Determine AI provider and model
	provider := p.config.AI.DefaultProvider
	if request.Provider != nil {
		provider = *request.Provider
	}

	var model string
	switch provider {
	case "claude":
		model = p.config.AI.Claude.Model
	case "gemini":
		model = p.config.AI.Gemini.Model
	default:
		return nil, NewInvalidInputError(fmt.Sprintf("unsupported provider: %s", provider), nil)
	}

	if request.Model != nil {
		model = *request.Model
	}

	// Step 5: Process with AI (placeholder for now)
	response, tokensUsed, err := p.processWithAI(ctx, conversation, request, provider, model)
	if err != nil {
		return nil, fmt.Errorf("AI processing failed: %w", err)
	}

	// Step 6: Add assistant response to conversation
	assistantMessage, err := p.context.AddMessage(ctx, conversation.ID, "assistant", response, nil)
	if err != nil {
		p.logger.Warn("Failed to add assistant message to conversation",
			slog.String("conversation_id", conversation.ID),
			slog.Any("error", err))
	}

	// Step 7: Build response
	queryResponse := &QueryResponse{
		Response:       response,
		ConversationID: conversation.ID,
		MessageID:      assistantMessage.ID,
		Provider:       provider,
		Model:          model,
		TokensUsed:     tokensUsed,
		ExecutionTime:  time.Since(startTime),
		Context:        make(map[string]interface{}),
	}

	// Step 8: Add tool execution results if any
	if len(request.Tools) > 0 {
		queryResponse.ToolsUsed = request.Tools
		// TODO: Add actual tool execution results
	}

	p.logger.Debug("Request processing completed",
		slog.String("conversation_id", conversation.ID),
		slog.String("message_id", assistantMessage.ID),
		slog.Duration("processing_time", queryResponse.ExecutionTime))

	return queryResponse, nil
}

// validateRequest validates the incoming request
func (p *Processor) validateRequest(request *QueryRequest) error {
	if request.Query == "" {
		return NewInvalidInputError("query cannot be empty", nil)
	}

	// Validate provider if specified
	if request.Provider != nil {
		validProviders := []string{"claude", "gemini"}
		valid := false
		for _, provider := range validProviders {
			if *request.Provider == provider {
				valid = true
				break
			}
		}
		if !valid {
			return NewInvalidInputError(fmt.Sprintf("invalid provider: %s", *request.Provider), nil)
		}
	}

	// Validate tools if specified
	if len(request.Tools) > 0 {
		for _, toolName := range request.Tools {
			if !p.registry.IsRegistered(toolName) {
				return NewToolNotFoundError(toolName)
			}
		}
	}

	return nil
}

// getOrCreateConversation gets an existing conversation or creates a new one
func (p *Processor) getOrCreateConversation(ctx context.Context, request *QueryRequest) (*Conversation, error) {
	// If conversation ID is provided, try to get existing conversation
	if request.ConversationID != nil {
		conversation, err := p.context.GetConversation(ctx, *request.ConversationID)
		if err != nil {
			// If conversation not found, create a new one
			if IsAssistantError(err) && GetAssistantError(err).Code == CodeContextNotFound {
				p.logger.Warn("Conversation not found, creating new one",
					slog.String("conversation_id", *request.ConversationID))
			} else {
				return nil, err
			}
		} else {
			return conversation, nil
		}
	}

	// Create new conversation
	userID := "default" // TODO: Get from authentication context
	if request.UserID != nil {
		userID = *request.UserID
	}

	title := p.generateConversationTitle(request.Query)
	conversation, err := p.context.CreateConversation(ctx, userID, title)
	if err != nil {
		return nil, err
	}

	return conversation, nil
}

// generateConversationTitle generates a title for a new conversation
func (p *Processor) generateConversationTitle(query string) string {
	// Simple title generation - take first 50 characters
	title := query
	if len(title) > 50 {
		title = title[:47] + "..."
	}
	return title
}

// processWithAI processes the request with the AI provider (placeholder)
func (p *Processor) processWithAI(ctx context.Context, conversation *Conversation, request *QueryRequest, provider, model string) (string, int, error) {
	// TODO: Implement actual AI provider integration
	// This is a placeholder implementation

	p.logger.Debug("Processing with AI provider",
		slog.String("provider", provider),
		slog.String("model", model),
		slog.String("conversation_id", conversation.ID))

	// Simulate AI processing
	response := fmt.Sprintf("This is a placeholder response for query: %s\n\nProvider: %s\nModel: %s\nConversation: %s",
		request.Query, provider, model, conversation.ID)

	// Simulate token usage
	tokensUsed := len(request.Query)/4 + len(response)/4 // Rough estimation

	// Simulate processing time
	time.Sleep(100 * time.Millisecond)

	return response, tokensUsed, nil
}

// Health checks the health of the processor
func (p *Processor) Health(ctx context.Context) error {
	// Check if we can access the database
	if err := p.db.Health(ctx); err != nil {
		return fmt.Errorf("database health check failed: %w", err)
	}

	// Check tool registry
	if err := p.registry.Health(ctx); err != nil {
		return fmt.Errorf("tool registry health check failed: %w", err)
	}

	return nil
}

// Stats returns processor statistics
func (p *Processor) Stats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// TODO: Implement actual statistics collection
	stats["requests_processed"] = 0
	stats["average_processing_time"] = "0ms"
	stats["active_conversations"] = 0
	stats["total_messages"] = 0

	return stats, nil
}

// Close closes the processor
func (p *Processor) Close(ctx context.Context) error {
	// Close context manager
	if err := p.context.Close(ctx); err != nil {
		return fmt.Errorf("failed to close context manager: %w", err)
	}

	return nil
}
