package gemini

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"sync"
	"time"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/observability"
)

// Client represents a Gemini API client
type Client struct {
	config     ai.ProviderConfig
	httpClient *http.Client
	logger     *slog.Logger
	stats      *ai.UsageStats
	statsMutex sync.RWMutex
}

// GeminiContent represents content in Gemini format
type GeminiContent struct {
	Parts []GeminiPart `json:"parts"`
	Role  string       `json:"role,omitempty"`
}

// GeminiPart represents a part of content
type GeminiPart struct {
	Text string `json:"text"`
}

// GeminiRequest represents a request to Gemini API
type GeminiRequest struct {
	Contents         []GeminiContent           `json:"contents"`
	GenerationConfig *GeminiGenerationConfig   `json:"generationConfig,omitempty"`
	SafetySettings   []GeminiSafetySetting     `json:"safetySettings,omitempty"`
	SystemInstruction *GeminiContent           `json:"systemInstruction,omitempty"`
}

// GeminiGenerationConfig represents generation configuration
type GeminiGenerationConfig struct {
	Temperature     *float64 `json:"temperature,omitempty"`
	TopP            *float64 `json:"topP,omitempty"`
	TopK            *int     `json:"topK,omitempty"`
	MaxOutputTokens *int     `json:"maxOutputTokens,omitempty"`
	StopSequences   []string `json:"stopSequences,omitempty"`
}

// GeminiSafetySetting represents safety settings
type GeminiSafetySetting struct {
	Category  string `json:"category"`
	Threshold string `json:"threshold"`
}

// GeminiResponse represents a response from Gemini API
type GeminiResponse struct {
	Candidates     []GeminiCandidate `json:"candidates"`
	UsageMetadata  *GeminiUsage      `json:"usageMetadata,omitempty"`
	PromptFeedback *PromptFeedback   `json:"promptFeedback,omitempty"`
}

// GeminiCandidate represents a candidate response
type GeminiCandidate struct {
	Content       GeminiContent    `json:"content"`
	FinishReason  string           `json:"finishReason"`
	Index         int              `json:"index"`
	SafetyRatings []SafetyRating   `json:"safetyRatings,omitempty"`
}

// GeminiUsage represents usage information from Gemini
type GeminiUsage struct {
	PromptTokenCount     int `json:"promptTokenCount"`
	CandidatesTokenCount int `json:"candidatesTokenCount"`
	TotalTokenCount      int `json:"totalTokenCount"`
}

// PromptFeedback represents feedback about the prompt
type PromptFeedback struct {
	BlockReason   string         `json:"blockReason,omitempty"`
	SafetyRatings []SafetyRating `json:"safetyRatings,omitempty"`
}

// SafetyRating represents a safety rating
type SafetyRating struct {
	Category    string `json:"category"`
	Probability string `json:"probability"`
}

// GeminiError represents an error from Gemini API
type GeminiError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Status  string `json:"status"`
}

// GeminiErrorResponse represents an error response from Gemini API
type GeminiErrorResponse struct {
	Error GeminiError `json:"error"`
}

// NewClient creates a new Gemini client
func NewClient(config ai.ProviderConfig, logger *slog.Logger) (*Client, error) {
	if config.APIKey == "" {
		return nil, fmt.Errorf("Gemini API key is required")
	}
	
	if config.BaseURL == "" {
		config.BaseURL = "https://generativelanguage.googleapis.com"
	}
	
	if config.Model == "" {
		config.Model = "gemini-pro"
	}
	
	if config.MaxTokens == 0 {
		config.MaxTokens = 4096
	}
	
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}

	httpClient := &http.Client{
		Timeout: config.Timeout,
	}

	return &Client{
		config:     config,
		httpClient: httpClient,
		logger:     observability.AILogger(logger, "gemini", config.Model),
		stats: &ai.UsageStats{
			TotalRequests:   0,
			TotalTokens:     0,
			InputTokens:     0,
			OutputTokens:    0,
			TotalCost:       0,
			AverageLatency:  0,
			ErrorRate:       0,
			RequestsPerHour: 0,
		},
	}, nil
}

// Name returns the provider name
func (c *Client) Name() string {
	return "gemini"
}

// GenerateResponse generates a response using Gemini API
func (c *Client) GenerateResponse(ctx context.Context, request *ai.GenerateRequest) (*ai.GenerateResponse, error) {
	startTime := time.Now()
	
	c.logger.Debug("Generating response with Gemini",
		slog.Int("message_count", len(request.Messages)),
		slog.String("model", c.getModel(request.Model)))

	// Convert messages to Gemini format
	contents := make([]GeminiContent, 0, len(request.Messages))
	var systemInstruction *GeminiContent
	
	for _, msg := range request.Messages {
		if msg.Role == "system" {
			// Gemini handles system messages as system instruction
			systemInstruction = &GeminiContent{
				Parts: []GeminiPart{{Text: msg.Content}},
			}
		} else {
			role := msg.Role
			if role == "assistant" {
				role = "model" // Gemini uses "model" instead of "assistant"
			}
			
			contents = append(contents, GeminiContent{
				Parts: []GeminiPart{{Text: msg.Content}},
				Role:  role,
			})
		}
	}
	
	// Use system prompt from request if provided
	if request.SystemPrompt != nil {
		systemInstruction = &GeminiContent{
			Parts: []GeminiPart{{Text: *request.SystemPrompt}},
		}
	}

	// Prepare generation config
	var genConfig *GeminiGenerationConfig
	if request.Temperature > 0 || request.MaxTokens > 0 {
		genConfig = &GeminiGenerationConfig{}
		if request.Temperature > 0 {
			genConfig.Temperature = &request.Temperature
		}
		if request.MaxTokens > 0 {
			maxTokens := c.getMaxTokens(request.MaxTokens)
			genConfig.MaxOutputTokens = &maxTokens
		}
	}

	// Prepare Gemini request
	geminiReq := GeminiRequest{
		Contents:          contents,
		GenerationConfig:  genConfig,
		SystemInstruction: systemInstruction,
		SafetySettings:    c.getDefaultSafetySettings(),
	}

	// Make API request
	response, err := c.makeRequest(ctx, geminiReq)
	if err != nil {
		c.updateErrorStats()
		return nil, err
	}

	// Extract content
	content := ""
	finishReason := "unknown"
	if len(response.Candidates) > 0 {
		candidate := response.Candidates[0]
		if len(candidate.Content.Parts) > 0 {
			content = candidate.Content.Parts[0].Text
		}
		finishReason = candidate.FinishReason
	}

	// Calculate response time
	responseTime := time.Since(startTime)

	// Extract token usage
	var tokenUsage ai.TokenUsage
	if response.UsageMetadata != nil {
		tokenUsage = ai.TokenUsage{
			InputTokens:  response.UsageMetadata.PromptTokenCount,
			OutputTokens: response.UsageMetadata.CandidatesTokenCount,
			TotalTokens:  response.UsageMetadata.TotalTokenCount,
		}
	}

	// Update statistics
	c.updateStats(tokenUsage.InputTokens, tokenUsage.OutputTokens, responseTime)

	// Build response
	aiResponse := &ai.GenerateResponse{
		Content:      content,
		Model:        c.getModel(request.Model),
		Provider:     "gemini",
		TokensUsed:   tokenUsage,
		FinishReason: finishReason,
		ResponseTime: responseTime,
		Metadata: map[string]interface{}{
			"candidates_count": len(response.Candidates),
		},
	}

	c.logger.Debug("Gemini response generated",
		slog.Int("input_tokens", tokenUsage.InputTokens),
		slog.Int("output_tokens", tokenUsage.OutputTokens),
		slog.Duration("response_time", responseTime))

	return aiResponse, nil
}

// GenerateEmbedding generates embeddings using Gemini API
func (c *Client) GenerateEmbedding(ctx context.Context, text string) (*ai.EmbeddingResponse, error) {
	startTime := time.Now()
	
	c.logger.Debug("Generating embedding with Gemini", slog.String("text_length", fmt.Sprintf("%d", len(text))))

	// Prepare embedding request
	reqBody := map[string]interface{}{
		"model": "models/embedding-001",
		"content": map[string]interface{}{
			"parts": []map[string]string{
				{"text": text},
			},
		},
	}

	// Make API request
	url := fmt.Sprintf("%s/v1beta/models/embedding-001:embedContent?key=%s", c.config.BaseURL, c.config.APIKey)
	
	reqBodyBytes, err := json.Marshal(reqBody)
	if err != nil {
		return nil, ai.NewProviderError(ai.ErrorTypeInvalidRequest, 
			fmt.Sprintf("failed to marshal request: %v", err), "gemini")
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBodyBytes))
	if err != nil {
		return nil, ai.NewProviderError(ai.ErrorTypeNetworkError, 
			fmt.Sprintf("failed to create request: %v", err), "gemini")
	}

	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, ai.NewProviderError(ai.ErrorTypeNetworkError, 
			fmt.Sprintf("request failed: %v", err), "gemini")
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, ai.NewProviderError(ai.ErrorTypeNetworkError, 
			fmt.Sprintf("failed to read response: %v", err), "gemini")
	}

	if resp.StatusCode != http.StatusOK {
		return nil, c.handleErrorResponse(resp.StatusCode, body)
	}

	// Parse embedding response
	var embeddingResp struct {
		Embedding struct {
			Values []float64 `json:"values"`
		} `json:"embedding"`
	}

	if err := json.Unmarshal(body, &embeddingResp); err != nil {
		return nil, ai.NewProviderError(ai.ErrorTypeServerError, 
			fmt.Sprintf("failed to parse response: %v", err), "gemini")
	}

	responseTime := time.Since(startTime)

	return &ai.EmbeddingResponse{
		Embedding:    embeddingResp.Embedding.Values,
		Model:        "embedding-001",
		Provider:     "gemini",
		TokensUsed:   c.estimateTokens(text),
		ResponseTime: responseTime,
	}, nil
}

// Health checks if Gemini API is accessible
func (c *Client) Health(ctx context.Context) error {
	// Simple health check with a minimal request
	request := &ai.GenerateRequest{
		Messages: []ai.Message{
			{Role: "user", Content: "Hello"},
		},
		MaxTokens: 10,
	}
	
	_, err := c.GenerateResponse(ctx, request)
	if err != nil {
		return fmt.Errorf("Gemini health check failed: %w", err)
	}
	
	return nil
}

// Close closes the client
func (c *Client) Close(ctx context.Context) error {
	// No cleanup needed for HTTP client
	return nil
}

// GetUsage returns usage statistics
func (c *Client) GetUsage(ctx context.Context) (*ai.UsageStats, error) {
	c.statsMutex.RLock()
	defer c.statsMutex.RUnlock()
	
	// Return a copy of the stats
	stats := *c.stats
	return &stats, nil
}

// makeRequest makes an HTTP request to Gemini API
func (c *Client) makeRequest(ctx context.Context, request GeminiRequest) (*GeminiResponse, error) {
	// Marshal request
	reqBody, err := json.Marshal(request)
	if err != nil {
		return nil, ai.NewProviderError(ai.ErrorTypeInvalidRequest, 
			fmt.Sprintf("failed to marshal request: %v", err), "gemini")
	}

	// Create HTTP request
	model := c.getModel("")
	url := fmt.Sprintf("%s/v1beta/models/%s:generateContent?key=%s", c.config.BaseURL, model, c.config.APIKey)
	
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, ai.NewProviderError(ai.ErrorTypeNetworkError, 
			fmt.Sprintf("failed to create request: %v", err), "gemini")
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")

	// Make request
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, ai.NewProviderError(ai.ErrorTypeNetworkError, 
			fmt.Sprintf("request failed: %v", err), "gemini")
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, ai.NewProviderError(ai.ErrorTypeNetworkError, 
			fmt.Sprintf("failed to read response: %v", err), "gemini")
	}

	// Handle error responses
	if resp.StatusCode != http.StatusOK {
		return nil, c.handleErrorResponse(resp.StatusCode, body)
	}

	// Parse successful response
	var geminiResp GeminiResponse
	if err := json.Unmarshal(body, &geminiResp); err != nil {
		return nil, ai.NewProviderError(ai.ErrorTypeServerError, 
			fmt.Sprintf("failed to parse response: %v", err), "gemini")
	}

	return &geminiResp, nil
}

// handleErrorResponse handles error responses from Gemini API
func (c *Client) handleErrorResponse(statusCode int, body []byte) error {
	var errorResp GeminiErrorResponse
	if err := json.Unmarshal(body, &errorResp); err != nil {
		return ai.NewProviderError(ai.ErrorTypeServerError, 
			fmt.Sprintf("HTTP %d: failed to parse error response", statusCode), "gemini")
	}

	errorType := ai.ErrorTypeServerError
	switch statusCode {
	case http.StatusUnauthorized, http.StatusForbidden:
		errorType = ai.ErrorTypeAuthentication
	case http.StatusTooManyRequests:
		errorType = ai.ErrorTypeRateLimit
	case http.StatusBadRequest:
		errorType = ai.ErrorTypeInvalidRequest
	case http.StatusRequestTimeout:
		errorType = ai.ErrorTypeTimeout
	}

	return ai.NewProviderError(errorType, errorResp.Error.Message, "gemini")
}

// getModel returns the model to use
func (c *Client) getModel(requestModel string) string {
	if requestModel != "" {
		return requestModel
	}
	return c.config.Model
}

// getMaxTokens returns the max tokens to use
func (c *Client) getMaxTokens(requestMaxTokens int) int {
	if requestMaxTokens > 0 {
		return requestMaxTokens
	}
	return c.config.MaxTokens
}

// getDefaultSafetySettings returns default safety settings
func (c *Client) getDefaultSafetySettings() []GeminiSafetySetting {
	return []GeminiSafetySetting{
		{Category: "HARM_CATEGORY_HARASSMENT", Threshold: "BLOCK_MEDIUM_AND_ABOVE"},
		{Category: "HARM_CATEGORY_HATE_SPEECH", Threshold: "BLOCK_MEDIUM_AND_ABOVE"},
		{Category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", Threshold: "BLOCK_MEDIUM_AND_ABOVE"},
		{Category: "HARM_CATEGORY_DANGEROUS_CONTENT", Threshold: "BLOCK_MEDIUM_AND_ABOVE"},
	}
}

// estimateTokens estimates token count for text
func (c *Client) estimateTokens(text string) int {
	// Rough estimation: ~4 characters per token
	return len(text) / 4
}

// updateStats updates usage statistics
func (c *Client) updateStats(inputTokens, outputTokens int, responseTime time.Duration) {
	c.statsMutex.Lock()
	defer c.statsMutex.Unlock()
	
	c.stats.TotalRequests++
	c.stats.InputTokens += int64(inputTokens)
	c.stats.OutputTokens += int64(outputTokens)
	c.stats.TotalTokens += int64(inputTokens + outputTokens)
	
	// Update average latency
	if c.stats.TotalRequests == 1 {
		c.stats.AverageLatency = responseTime
	} else {
		c.stats.AverageLatency = time.Duration(
			(int64(c.stats.AverageLatency)*(c.stats.TotalRequests-1) + int64(responseTime)) / c.stats.TotalRequests,
		)
	}
	
	now := time.Now()
	c.stats.LastRequestTime = &now
}

// updateErrorStats updates error statistics
func (c *Client) updateErrorStats() {
	c.statsMutex.Lock()
	defer c.statsMutex.Unlock()
	
	c.stats.TotalRequests++
}
